Sub SubstituirQuebrasLinhaEmTodosDocx()
'
' Macro para WPS Office Writer
' Substitui quebras de linha manuais (^l) por quebras de parágrafo (^p)
' em todos os arquivos DOCX das pastas do projeto Concurseiro
'
' Autor: Assistente IA
' Data: 16/06/2025
'

    Dim fso As Object
    Dim pastaBase As String
    Dim pastas As Variant
    Dim i As Integer
    Dim totalArquivos As Integer
    Dim arquivosProcessados As Integer
    Dim arquivosComSubstituicoes As Integer
    
    ' Configurar pasta base
    pastaBase = "C:\Obsidian Vault\Teste\"
    
    ' Lista de pastas para processar
    pastas = Array("Administrativo", "Civil", "Constitucional", "Financeiro", _
                   "História GO", "Legislação GO", "Leis", "Previdenciário", _
                   "Processo Civil", "Processo Civil - Fazenda Pública", _
                   "Processo Legislativo", "Tributário")
    
    ' Inicializar contadores
    totalArquivos = 0
    arquivosProcessados = 0
    arquivosComSubstituicoes = 0
    
    ' Criar objeto FileSystemObject
    Set fso = CreateObject("Scripting.FileSystemObject")
    
    ' Mostrar mensagem inicial
    MsgBox "Iniciando processamento de substituição de quebras de linha." & vbCrLf & _
           "Isso pode levar alguns minutos...", vbInformation, "Processamento Iniciado"
    
    ' Processar cada pasta
    For i = 0 To UBound(pastas)
        Dim caminhoCompleto As String
        caminhoCompleto = pastaBase & pastas(i)
        
        ' Verificar se a pasta existe
        If fso.FolderExists(caminhoCompleto) Then
            Call ProcessarPasta(caminhoCompleto, totalArquivos, arquivosProcessados, arquivosComSubstituicoes)
        Else
            MsgBox "Pasta não encontrada: " & caminhoCompleto, vbExclamation
        End If
    Next i
    
    ' Mostrar resultado final
    Dim mensagemFinal As String
    mensagemFinal = "Processamento concluído!" & vbCrLf & vbCrLf & _
                   "Estatísticas:" & vbCrLf & _
                   "• Total de arquivos encontrados: " & totalArquivos & vbCrLf & _
                   "• Arquivos processados: " & arquivosProcessados & vbCrLf & _
                   "• Arquivos com substituições: " & arquivosComSubstituicoes & vbCrLf & vbCrLf & _
                   "Todos os arquivos foram verificados e corrigidos se necessário."
    
    MsgBox mensagemFinal, vbInformation, "Processamento Concluído"
    
End Sub

Sub ProcessarPasta(caminhoPasta As String, ByRef totalArquivos As Integer, _
                   ByRef arquivosProcessados As Integer, ByRef arquivosComSubstituicoes As Integer)
'
' Processa todos os arquivos DOCX de uma pasta específica
'
    Dim fso As Object
    Dim pasta As Object
    Dim arquivo As Object
    Dim doc As Document
    Dim nomeArquivo As String
    Dim substituicoes As Long
    
    Set fso = CreateObject("Scripting.FileSystemObject")
    Set pasta = fso.GetFolder(caminhoPasta)
    
    ' Processar cada arquivo na pasta
    For Each arquivo In pasta.Files
        If LCase(Right(arquivo.Name, 5)) = ".docx" And _
           Left(arquivo.Name, 2) <> "~$" Then ' Ignorar arquivos temporários
            
            totalArquivos = totalArquivos + 1
            nomeArquivo = arquivo.Path
            
            ' Tentar abrir e processar o arquivo
            On Error GoTo ErroProcessamento
            
            ' Abrir documento
            Set doc = Documents.Open(nomeArquivo, ReadOnly:=False)
            
            ' Realizar substituições
            substituicoes = SubstituirQuebrasNoDocumento(doc)
            
            ' Se houve substituições, salvar o documento
            If substituicoes > 0 Then
                doc.Save
                arquivosComSubstituicoes = arquivosComSubstituicoes + 1
            End If
            
            ' Fechar documento
            doc.Close SaveChanges:=False
            arquivosProcessados = arquivosProcessados + 1
            
            GoTo ProximoArquivo
            
ErroProcessamento:
            MsgBox "Erro ao processar arquivo: " & nomeArquivo & vbCrLf & _
                   "Erro: " & Err.Description, vbExclamation
            If Not doc Is Nothing Then
                doc.Close SaveChanges:=False
            End If
            Resume ProximoArquivo
            
ProximoArquivo:
        End If
    Next arquivo
    
End Sub

Function SubstituirQuebrasNoDocumento(doc As Document) As Long
'
' Realiza as substituições de quebras de linha no documento
' Retorna o número total de substituições realizadas
'
    Dim totalSubstituicoes As Long
    Dim substituicoesAtual As Long
    
    totalSubstituicoes = 0
    
    ' Substituir quebras de linha manuais (^l) por quebras de parágrafo (^p)
    With doc.Range.Find
        .ClearFormatting
        .Replacement.ClearFormatting
        .Text = "^l"
        .Replacement.Text = "^p"
        .Forward = True
        .Wrap = wdFindContinue
        .Format = False
        .MatchCase = False
        .MatchWholeWord = False
        .MatchWildcards = False
        .MatchSoundsLike = False
        .MatchAllWordForms = False
        
        ' Contar e executar substituições
        Do While .Execute(Replace:=True)
            totalSubstituicoes = totalSubstituicoes + 1
            ' Evitar loop infinito
            If totalSubstituicoes > 10000 Then Exit Do
        Loop
    End With
    
    ' Também verificar quebras de linha verticais (^v) se existirem
    With doc.Range.Find
        .ClearFormatting
        .Replacement.ClearFormatting
        .Text = "^v"
        .Replacement.Text = "^p"
        .Forward = True
        .Wrap = wdFindContinue
        .Format = False
        .MatchCase = False
        .MatchWholeWord = False
        .MatchWildcards = False
        .MatchSoundsLike = False
        .MatchAllWordForms = False
        
        ' Contar e executar substituições
        Do While .Execute(Replace:=True)
            totalSubstituicoes = totalSubstituicoes + 1
            ' Evitar loop infinito
            If totalSubstituicoes > 10000 Then Exit Do
        Loop
    End With
    
    SubstituirQuebrasNoDocumento = totalSubstituicoes
    
End Function
