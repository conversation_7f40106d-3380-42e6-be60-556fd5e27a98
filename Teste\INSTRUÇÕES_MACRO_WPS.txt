INSTRUÇÕES PARA USO DA MACRO WPS OFFICE
======================================

OBJETIVO:
Substituir quebras de linha manuais (^l) por quebras de parágrafo (^p) 
em todos os arquivos DOCX do projeto Concurseiro.

ARQUIVO DA MACRO:
Macro_WPS_Substituir_Quebras.bas

COMO INSTALAR E EXECUTAR A MACRO:
=================================

PASSO 1 - ABRIR O WPS OFFICE WRITER:
1. Abra o WPS Office Writer
2. Crie um documento em branco ou abra qualquer documento

PASSO 2 - ACESSAR O EDITOR DE MACROS:
1. Pressione Alt + F11 (ou vá em Ferramentas > Macro > Editor Visual Basic)
2. Isso abrirá o Editor VBA do WPS Office

PASSO 3 - IMPORTAR A MACRO:
1. No Editor VBA, clique com o botão direito em "VBAProject"
2. Selecione "Import File..." (Importar Arquivo)
3. Navegue até a pasta: C:\Obsidian Vault\Teste\
4. Selecione o arquivo: Macro_WPS_Substituir_Quebras.bas
5. Clique em "Abrir"

PASSO 4 - EXECUTAR A MACRO:
1. No Editor VBA, localize o módulo importado
2. Encontre a sub-rotina "SubstituirQuebrasLinhaEmTodosDocx"
3. Clique dentro da sub-rotina
4. Pressione F5 ou clique no botão "Run" (Executar)

ALTERNATIVA - EXECUTAR PELO MENU:
1. Feche o Editor VBA
2. No WPS Writer, vá em Ferramentas > Macro > Macros
3. Selecione "SubstituirQuebrasLinhaEmTodosDocx"
4. Clique em "Executar"

O QUE A MACRO FAZ:
==================

PASTAS PROCESSADAS:
✓ Administrativo
✓ Civil
✓ Constitucional
✓ Financeiro
✓ História GO
✓ Legislação GO
✓ Leis
✓ Previdenciário
✓ Processo Civil
✓ Processo Civil - Fazenda Pública
✓ Processo Legislativo
✓ Tributário

SUBSTITUIÇÕES REALIZADAS:
• ^l (quebra de linha manual) → ^p (quebra de parágrafo)
• ^v (quebra de linha vertical) → ^p (quebra de parágrafo)

RECURSOS DA MACRO:
• Processa automaticamente todas as 12 pastas
• Ignora arquivos temporários (~$)
• Conta o número de substituições realizadas
• Salva apenas arquivos que foram modificados
• Exibe relatório final com estatísticas
• Tratamento de erros para arquivos problemáticos

TEMPO ESTIMADO:
Aproximadamente 2-5 minutos para processar todos os 179 arquivos DOCX

BACKUP RECOMENDADO:
Embora a macro seja segura, recomenda-se fazer backup da pasta 
C:\Obsidian Vault\Teste\ antes de executar a macro.

MENSAGENS DA MACRO:
===================

INÍCIO:
"Iniciando processamento de substituição de quebras de linha.
Isso pode levar alguns minutos..."

FINAL:
"Processamento concluído!

Estatísticas:
• Total de arquivos encontrados: [número]
• Arquivos processados: [número]  
• Arquivos com substituições: [número]

Todos os arquivos foram verificados e corrigidos se necessário."

SOLUÇÃO DE PROBLEMAS:
====================

ERRO: "Pasta não encontrada"
Solução: Verifique se o caminho C:\Obsidian Vault\Teste\ está correto

ERRO: "Erro ao processar arquivo"
Solução: Arquivo pode estar aberto em outro programa. Feche todos os 
arquivos DOCX e tente novamente.

ERRO: "Macro não encontrada"
Solução: Certifique-se de que importou corretamente o arquivo .bas

COMPATIBILIDADE:
================
✓ WPS Office Writer (todas as versões recentes)
✓ Microsoft Word (com pequenas adaptações se necessário)
✓ Windows 10/11
✓ Arquivos .docx

OBSERVAÇÕES IMPORTANTES:
========================
1. A macro processa apenas arquivos .docx
2. Arquivos temporários (~$) são ignorados automaticamente
3. Apenas arquivos com substituições são salvos
4. A macro é segura e não altera a formatação existente
5. Processo é reversível (pode desfazer se necessário)

SUPORTE:
========
Em caso de dúvidas ou problemas, consulte a documentação do WPS Office
sobre macros VBA ou entre em contato com o suporte técnico.
